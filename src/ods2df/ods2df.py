from icecream import ic

import base64
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.decrepit.ciphers.algorithms import Blowfish
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import io  # For BytesIO
import pandas as pd
import re  # For time parsing
from xml.etree import ElementTree as ET
import zipfile
import zlib


# Namespaces
NS = {
    'table': 'urn:oasis:names:tc:opendocument:xmlns:table:1.0',
    'office': 'urn:oasis:names:tc:opendocument:xmlns:office:1.0',
    'style': 'urn:oasis:names:tc:opendocument:xmlns:style:1.0',
    'number': 'urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0',
    'fo': 'urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0',
    'text': 'urn:oasis:names:tc:opendocument:xmlns:text:1.0',
    'calc': 'urn:org:documentfoundation:names:experimental:calc:xmlns:calc:1.0'
}


def decrypt_file(zf, file_name, password, manifest_root, manifest_ns, backend):
    """Decrypts and reads a file from an ODS ZIP archive if it's encrypted.

    This function handles decryption based on the manifest's encryption parameters.
    It supports AES-256-CBC and Blowfish-CFB algorithms, decompresses the data if needed,
    and verifies checksums. If the file is not encrypted, it reads it directly.

    Args:
    zf (zipfile.ZipFile): The opened ZIP file object for the ODS file.
    file_name (str): The name of the file to decrypt and read (e.g., 'content.xml').
    password (str): The password for decryption (required if encrypted).
    manifest_root (xml.etree.ElementTree.Element): The root of the manifest XML.
    manifest_ns (dict): Namespace dictionary for manifest XML parsing.
    backend (cryptography.hazmat.backends.Backend): Cryptography backend.

    Returns:
    bytes: The decrypted (and decompressed) content of the file.

    Raises:
    ValueError: If the file is not found, unsupported algorithm, wrong password,
    checksum mismatch, or decompression fails.

    Notes:
    - Uses PBKDF2 for key derivation and zlib for decompression.
    - Warnings are printed for non-critical issues like checksum mismatches in legacy formats.
    """
    entry = manifest_root.find(f".//manifest:file-entry[@manifest:full-path='{file_name}']", manifest_ns)
    if entry is None:
        raise ValueError(f"{file_name} not found in manifest.")

    enc_data = entry.find('manifest:encryption-data', manifest_ns)

    if enc_data is not None:
        if password is None:
            raise ValueError("File is password-protected but no password provided.")

        # Proceed with decryption
        alg = enc_data.find('manifest:algorithm', manifest_ns)
        key_deriv = enc_data.find('manifest:key-derivation', manifest_ns)
        start_key_gen = enc_data.find('manifest:start-key-generation', manifest_ns)  # Optional in some versions

        # Parse params using qualified attribute names
        algorithm_name = ic(alg.get(f"{{{manifest_ns['manifest']}}}algorithm-name"))
        iv = base64.b64decode(alg.get(f"{{{manifest_ns['manifest']}}}initialisation-vector"))
        salt = base64.b64decode(key_deriv.get(f"{{{manifest_ns['manifest']}}}salt"))
        iteration_count = int(key_deriv.get(f"{{{manifest_ns['manifest']}}}iteration-count"))
        key_size = int(key_deriv.get(f"{{{manifest_ns['manifest']}}}key-size", 32))    # 256-bit
        checksum_type = enc_data.get(f"{{{manifest_ns['manifest']}}}checksum-type", 'SHA256/1K')   # Common default
        checksum = base64.b64decode(enc_data.get(f"{{{manifest_ns['manifest']}}}checksum"))

        if 'aes256-cbc' in algorithm_name.lower():
            cipher_algorithm = algorithms.AES
            mode = modes.CBC(iv)
            use_pkcs7_padding = True
        elif 'blowfish' in algorithm_name.lower() and 'cfb' in algorithm_name.lower():
            if len(iv) != 8:
                raise ValueError("Invalid IV length for Blowfish CFB.")
            cipher_algorithm = Blowfish
            mode = modes.CFB(iv)
            if key_size != 16:  # Adjust to common Blowfish key size
                key_size = 16
            use_pkcs7_padding = False  # Try without padding for Blowfish CFB
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm_name}.")

        # Determine hash algorithm for start-key and checksum
        start_hash_alg = hashes.SHA256()
        check_hash_alg = hashes.SHA256()
        if start_key_gen is not None:
            start_key_name = ic(start_key_gen.get(f"{{{manifest_ns['manifest']}}}start-key-generation-name", 'SHA256'))
            if 'SHA1' in start_key_name.upper():
                start_hash_alg = hashes.SHA1()
            if 'SHA1' in checksum_type.upper():
                check_hash_alg = hashes.SHA1()

        # For PBKDF2, force SHA1 (common in some ODF impls, even if start is SHA256)
        kdf_hash_alg = hashes.SHA1()  # Test this; change to hashes.SHA256() if it fails

        password_bytes = password.encode('utf-8')
        # If start-key-generation is present, pre-hash password to get start key
        if start_key_gen is not None:
            start_hash = hashes.Hash(start_hash_alg, backend=backend)
            start_hash.update(password_bytes)
            password_bytes = start_hash.finalize()  # Use this as input to PBKDF2

        kdf = PBKDF2HMAC(
            algorithm=kdf_hash_alg,
            length=key_size,
            salt=salt,
            iterations=iteration_count,
            backend=backend
        )
        key = kdf.derive(password_bytes)

        # Extract encrypted content.xml bytes
        with zf.open(file_name) as encrypted_file:
            encrypted_data = encrypted_file.read()

        # Decrypt
        cipher = Cipher(cipher_algorithm(key), mode, backend=backend)
        decryptor = cipher.decryptor()
        decrypted_data = decryptor.update(
            encrypted_data) + decryptor.finalize()  # Raw decrypted (possibly compressed)

        # Conditional padding removal
        if use_pkcs7_padding:
            # Remove PKCS7 padding (ODF uses PKCS7)
            padding_len = decrypted_data[-1]
            decrypted_compressed = decrypted_data[:-padding_len]
        else:
            decrypted_compressed = decrypted_data  # No padding for Blowfish

        ic(f"First 20 decrypted_compressed bytes: {decrypted_compressed[:20]}")  # Debug

        window_bits_options = [-zlib.MAX_WBITS, -8, 15, 0]  # Try raw DEFLATE, minimal, with header, auto
        decrypted_xml = None
        for wb in window_bits_options:
            try:
                decrypted_xml = zlib.decompress(decrypted_compressed, wb)
                ic(f"Successful decompression with window bits: {wb}")
                ic(f"First 20 decrypted bytes: {decrypted_xml[:20]}")
                break
            except zlib.error as e:
                ic(f"Decompression failed with {wb}: {e}")

        if decrypted_xml is None:
            raise ValueError(
                "All decompression attempts failed. Possible wrong password, corruption, or format mismatch.")

        # Verify checksum (of first 1024 bytes of uncompressed data, or as per checksum-type)
        if 'aes' in algorithm_name.lower() and '1K' in checksum_type:  # Only enforce for AES
            check_hash = hashes.Hash(check_hash_alg, backend=backend)
            check_hash.update(decrypted_xml[:1024])
            computed_checksum = check_hash.finalize()
            if computed_checksum != checksum:
                raise ValueError("Checksum mismatch. Wrong password or corrupted data.")
        # For Blowfish, optionally try alternative checksum (e.g., of compressed data)
        elif 'blowfish' in algorithm_name.lower() and '1K' in checksum_type:
            try:
                check_hash = hashes.Hash(check_hash_alg, backend=backend)
                check_hash.update(decrypted_compressed[:1024])  # Try compressed instead
                computed_checksum = check_hash.finalize()
                if computed_checksum != checksum:
                    print("Warning: Checksum mismatch on compressed data; proceeding anyway for legacy file.")
            except:
                print("Warning: Checksum verification skipped for Blowfish.")
        return decrypted_xml
    else:
        # File is not password-protected; read directly (zipfile handles any compression)
        if password is not None:
            print("Warning: Password provided but file is not protected. Ignoring password.")
        with zf.open(file_name) as f:
            return f.read()


def get_col_dec_places(content_root, sheet_name):
    """Extracts decimal places for columns in a specific sheet from the parsed content XML.

    This function analyzes column styles to determine decimal places for numeric cells,
    used as a fallback for deciding float vs. int types during data extraction.

    Args:
    content_root (xml.etree.ElementTree.Element): The root of the parsed content XML.
    sheet_name (str): The name of the sheet to analyze.

    Returns:
    list[str]: A list of decimal places for each column (e.g., '2' or 'Unspecified').

    Raises:
    ValueError: If the specified sheet is not found in the XML.
    """
    auto_styles = content_root.find('office:automatic-styles', NS) or content_root.find('.//office:automatic-styles', NS)

    sheet = None
    for s in content_root.findall('.//table:table', NS):
        if s.get(f"{{{NS['table']}}}name") == sheet_name:
            sheet = s
            break
    if sheet is None:
        raise ValueError(f"Sheet '{sheet_name}' not found.")

    # Calculate effective max columns
    max_cols = 0
    for row in sheet.findall('table:table-row', NS):
        current_col = 0
        for cell in row.findall('table:table-cell', NS) + row.findall('table:covered-table-cell', NS):
            repeat = int(cell.get(f"{{{NS['table']}}}number-columns-repeated") or 1)
            if (cell.get(f"{{{NS['office']}}}value") is not None or
                cell.get(f"{{{NS['office']}}}value-type") is not None or
                cell.find('text:p', NS) is not None):
                max_cols = max(max_cols, current_col + repeat)
            current_col += repeat
    max_cols = max(max_cols, 1)

    # Collect column styles
    col_dec_places = []
    col_is_numeric = []
    col_index = 0
    for col_elem in sheet.findall('table:table-column', NS):
        default_style_name = col_elem.get(f"{{{NS['table']}}}default-cell-style-name")
        repeat = int(col_elem.get(f"{{{NS['table']}}}number-columns-repeated") or 1)
        for _ in range(min(repeat, max_cols - col_index)):
            dec_places = "Unspecified"
            is_numeric = False
            if default_style_name and auto_styles is not None:
                for sty in auto_styles.findall("style:style", NS):
                    if (sty.get(f"{{{NS['style']}}}name") == default_style_name and
                        sty.get(f"{{{NS['style']}}}family") == 'table-cell'):
                        num_elem = sty.find('.//number:number', NS) or sty.find('.//number:number-style', NS)
                        if num_elem is not None:
                            is_numeric = True
                            dec_places = num_elem.get(f"{{{NS['number']}}}decimal-places") or "Unspecified"
                        break
            col_dec_places.append(dec_places)
            col_is_numeric.append(is_numeric)
            col_index += 1
            if col_index >= max_cols:
                break

    return col_dec_places


def extract_cell_text(cell_elem, ns):
    """Extract text from a cell, handling multi-line strings properly by:
    1. Finding all text:p elements in the cell
    2. Extracting text from each paragraph, including handling line breaks and nested elements
    3. Joining multiple paragraphs with newlines

    Args:
    cell_elem: The XML element representing the table cell
    ns: Namespace dictionary for XML parsing

    Returns:
    str: The complete text content with proper line breaks
    """
    # Handle multi-line strings by collecting all text:p elements
    p_elements = cell_elem.findall('text:p', ns)
    if p_elements:
        lines = []
        for p_elem in p_elements:
            # Recursively get all text, handling nested spans and line breaks
            line_text = ''
            queue = [p_elem]
            while queue:
                elem = queue.pop(0)
                if elem.text:
                    line_text += elem.text
                for child in elem:
                    if child.tag.endswith('line-break'):
                        line_text += '\n'
                    else:
                        queue.append(child)
                    if child.tail:
                        line_text += child.tail
            lines.append(line_text.strip())
        return '\n'.join(lines)
    return ''


def read_ods_sheet_to_dataframe(file_path, sheet_name, password=None):
    """Reads a specific sheet from an ODS file into a Pandas DataFrame.

    Handles both password-protected and unprotected ODS files by decrypting content
    and styles if necessary. Extracts cell values, handling types like floats, integers,
    dates, times, formulas, and more, while managing repeated rows/columns and trimming empties.

    Args:
    file_path (str): Path to the ODS file.
    sheet_name (str): Name of the sheet to read.
    password (str, optional): Password for encrypted files. Defaults to None.

    Returns:
    pandas.DataFrame: DataFrame containing the sheet's data.

    Raises:
    ValueError: If the file is encrypted without a password, sheet not found,
    no spreadsheet body, or decryption/decompression fails.

    Notes:
    - Uses ElementTree for XML parsing and cryptography for decryption.
    - Trims trailing empty rows and pads columns with None for consistency.
    - Debug output via icecream (ic) for troubleshooting.
    """
    backend = default_backend()

    with zipfile.ZipFile(file_path, 'r') as zf:
        # Extract manifest.xml (not encrypted)
        with zf.open('META-INF/manifest.xml') as manifest_file:
            manifest_tree = ET.parse(manifest_file)
            manifest_root = manifest_tree.getroot()

        manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

        # Decrypt and read content.xml
        decrypted_xml = decrypt_file(zf, 'content.xml', password, manifest_root, manifest_ns, backend)

        # Parse the decrypted XML
        content_tree = ET.parse(io.BytesIO(decrypted_xml))
        content_root = content_tree.getroot()

        styles_root = None
        if 'styles.xml' in zf.namelist():
            styles_xml = decrypt_file(zf, 'styles.xml', password, manifest_root, manifest_ns, backend)
            styles_tree = ET.parse(io.BytesIO(styles_xml))
            styles_root = styles_tree.getroot()

        # Call get_col_dec_places with parsed content_root
        col_dec_places = get_col_dec_places(content_root, sheet_name)

        ns = NS
        # Collect all styles: from content.xml automatic-styles and styles.xml if present
        all_styles = {}
        auto_styles = content_root.find('office:automatic-styles', ns) or ET.Element('dummy')
        for style_elem in auto_styles.iterfind('style:style', ns):  # Use iterfind for deeper search
            name = style_elem.get(f"{{{ns['style']}}}name")
            if name:
                all_styles[name] = style_elem
        if styles_root:
            office_styles = styles_root.find('office:styles', ns) or ET.Element('dummy')
            for style_elem in office_styles.iterfind('style:style', ns):
                name = style_elem.get(f"{{{ns['style']}}}name")
                if name:
                    all_styles[name] = style_elem

        # Also collect number-styles directly
        for data_style in styles_root.iterfind('.//number:number-style', ns):
            name = data_style.get(f"{{{ns['style']}}}name") or data_style.get(f"{{{ns['number']}}}name")
            if name:
                all_styles[name] = data_style

        # Function to get decimal places recursively (handle style parents)
        def get_decimal_places(style_name):
            if not style_name or style_name not in all_styles:
                return -1  # Unknown
            style_elem = all_styles[style_name]
            # Check for data-style-name
            data_style_name = style_elem.get(f"{{{ns['style']}}}data-style-name")
            if data_style_name and data_style_name in all_styles:
                data_style_elem = all_styles[data_style_name]
            else:
                data_style_elem = style_elem
            # Find number:number
            number_elem = data_style_elem.find('number:number', ns)
            if number_elem is not None:
                return int(ic(number_elem.get(f"{{{ns['number']}}}decimal-places", -1)))
            parent_name = style_elem.get(f"{{{ns['style']}}}parent-style-name")
            # If not found, check parent style
            if parent_name:
                return get_decimal_places(parent_name)
            return -1  # Not found

        # Find the spreadsheet body
        spreadsheet = content_root.find('.//office:body/office:spreadsheet', ns)
        if spreadsheet is None:
            raise ValueError("No spreadsheet found in the document.")

        # Find the specific sheet (using qualified attribute for table:name)
        sheet = None
        for s in spreadsheet.findall('table:table', ns):
            current_name = s.get(f"{{{ns['table']}}}name")
            if current_name == sheet_name:
                sheet = s
                break
        if sheet is None:
            raise ValueError(f"Sheet '{sheet_name}' not found.")

        # Extract data, handling repeats and basic value types
        data = []
        max_cols = 0
        for row_elem in sheet.findall('table:table-row', ns):
            row_repeat = int(row_elem.get(f"{{{ns['table']}}}number-rows-repeated", '1'))
            row_data = []

            # Collect cell elements to identify the last one
            cell_elems = list(row_elem.iterfind('table:table-cell', ns))
            for i, cell_elem in enumerate(cell_elems):
                cell_repeat = int(cell_elem.get(f"{{{ns['table']}}}number-columns-repeated", '1'))
                value_type = ic(cell_elem.get(f"{{{ns['office']}}}value-type"))
                formula = cell_elem.get(f"{{{ns['table']}}}formula")  # Check for formula attribute

                # Namespace-agnostic lookup for <p> (handles varying URIs like calc or text)
                p = next((child for child in cell_elem if child.tag.endswith('p')), None)

                if formula:
                    # Strip OpenDocument namespace prefix from formula
                    if formula.startswith('of:='):
                        value = '=' + formula[4:]  # Remove 'of:=' and add '='
                    elif formula.startswith('oooc:='):
                        value = '=' + formula[6:]  # Remove 'oooc:=' and add '='
                    else:
                        value = formula  # Keep as-is if no known prefix
                elif value_type == 'float':
                    float_str = cell_elem.get(f"{{{ns['office']}}}value", '0')
                    float_value = float(float_str)
                    
                    # Check if this is actually a number by looking at text content
                    text_content = extract_cell_text(cell_elem, ns)
                    if text_content and not re.match('[-]?[0-9.]+', text_content):
                        # Can be currency displayed as text, treat as string
                        value = text_content
                    else:
                        # Regular float handling
                        # Get decimal places from style (recursive)
                        style_name = ic(cell_elem.get(f"{{{ns['table']}}}style-name"))
                        decimal_places = ic(get_decimal_places(style_name))
                        # Decide type
                        if decimal_places == 0:
                            value = int(float_value)
                        elif decimal_places > 0:
                            value = float_value
                        else:  # Unknown or no style: fallback to is_integer check
                            if i < len(col_dec_places) and col_dec_places[i] in ('Unspecified', '0') \
                               and float_value.is_integer():
                                ic(col_dec_places[i])
                                value = int(float_value)
                            else:
                                value = float_value
                elif value_type == 'percentage':
                    pct_value = float(cell_elem.get(f"{{{ns['office']}}}value", '0'))  # Raw float, e.g., 0.75
                    value = f"{pct_value * 100:.0f}%"  # e.g., '75%' (adjust .0f for decimals if needed)
                elif value_type == 'currency':
                    float_value = float(cell_elem.get(f"{{{ns['office']}}}value", '0'))
                    currency_symbol = cell_elem.get(f"{{{ns['office']}}}currency", '')  # e.g., 'USD'
                    value = f"{currency_symbol} {float_value}"  # String like 'USD 123.45';
                elif value_type == 'date':
                    value = cell_elem.get(f"{{{ns['office']}}}date-value")  # ISO date string
                elif value_type == 'time':
                    time_value = cell_elem.get(f"{{{ns['office']}}}time-value")  # e.g., 'PT12H30M00S'
                    if time_value:
                        # Parse ISO duration: Extract hours, minutes, seconds
                        # Simple regex: Assumes format PTxHyMzS
                        match = re.match(r'PT(\d+)H(\d+)M(\d+)S', time_value)
                        if match:
                            hours, minutes, seconds = match.groups()
                            value = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"  # e.g., '12:30:00'
                            # Optional: Add default timezone if desired (e.g., UTC)
                            # value += " UTC"  # Uncomment for '12:30:00 UTC'
                        else:
                            value = time_value  # Fallback to raw if parsing fails
                    else:
                        value = None
                elif value_type == 'boolean':
                    bool_str = cell_elem.get(f"{{{ns['office']}}}boolean-value")
                    value = True if bool_str == 'true' else False if bool_str == 'false' else None
                elif value_type == 'string' or (value_type is None and p is not None):
                    value = extract_cell_text(cell_elem, ns)
                else:
                    ic("i: {0}".format(i))    # ic for debug
                    ic("value type: {0}".format(value_type))    # ic for debug
                    ic(cell_elem.get(f"{{{ns['office']}}}currency", ''))    # ic for debug
                    value = None  # For images, objects, void, or unsupported types, treat as empty

                # Skip extending if this is the last cell and it's empty (trailing empty repeat)
                if i == len(cell_elems) - 1 and value is None:
                    continue

                row_data.extend([value] * cell_repeat)

            # Update max_cols based on effective length (without trailing empties)
            max_cols = max(max_cols, len(row_data))

            # Extend data with the repeated rows
            data.extend([row_data] * row_repeat)

        # Pad all rows to max_cols with None
        for i in range(len(data)):
            data[i] += [None] * (max_cols - len(data[i]))

        ic(f"Rows before trimming: {len(data)}")  # Debug
        # Trim trailing empty rows (all None) - only continuous from the end
        while data and all(v is None for v in data[-1]):
            data.pop()
        ic(f"Rows after trimming: {len(data)}")  # Debug

        df = pd.DataFrame(data)
        return df