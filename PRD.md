Product Requirement Document (PRD): ODS to Pandas DataFrame Converter

# Product Requirement Document (PRD): ODS to Pandas DataFrame Converter

## 1. Introduction

### 1.1 Overview
    
This PRD outlines the requirements for a Python function named read_ods_sheet_to_dataframe that reads an OpenDocument Spreadsheet (.ods) file and converts a specified sheet into a Pandas DataFrame. The function is designed to handle both unprotected and password-protected ODS files, extracting tabular data while managing encryption, XML parsing, and data formatting nuances inherent to the ODS format.

### 1.2 Scope
- **In Scope**: Reading and parsing ODS files, decryption for password-protected files, cell value extraction (floats, strings, percentages, dates, times, booleans, formulas, currency), multi-line string support, handling repeated rows/columns, and trimming trailing empty rows. Header rows are not promoted to DataFrame columns; they are imported as regular rows.
- **Out of Scope**: Advanced cell types (e.g., images, objects), formula computation, merged cell interpretation, multi-sheet processing in a single call, writing/modifying ODS files, currency symbol normalization, or support for non-ODS formats.

## 2. Objectives

- Enable reading of specific sheets from ODS files into Pandas DataFrames.
- Support decryption of password-protected ODS files using standard ODF encryption methods.
- Handle common ODS structural elements like repeated rows and columns.
- Ensure data integrity by trimming unnecessary empty rows and padding incomplete rows.
- Maintain compatibility with Python 3.x environments using minimal dependencies.

## 3. Features and Functionality

### 3.1 Core Features

- **ODS File Reading**: Parse the content.xml from the zipped ODS structure.
- **Sheet Selection**: Extract data from a named sheet (case-sensitive match).
- **Password Protection Handling**:
    - Detect and decrypt encrypted content.xml using AES-256-CBC or Blowfish CFB with PBKDF2 key derivation, including legacy adaptations for Blowfish.
    - Support for optional password input; raise errors if password is required but not provided.
    - Fallback mechanisms for decompression and hashing (e.g., SHA1/SHA256 based on manifest).
- **Data Extraction**:
    - Handle cell types: floats (via office:value), strings (via text:p elements), percentages, dates, times, booleans, formulas, and currency values.
    - Support for repeated columns (number-columns-repeated) and rows (number-rows-repeated).
    - Pad rows to uniform length with None values for incomplete data.
    - **Multi-line String Support**: Properly extract multi-line text content by collecting all text:p elements within a cell and joining them with newlines.
    - **Currency Handling**: Extract currency values as strings, preserving the display format. Note: Currency symbol representation may vary depending on cell style formatting (see Known Limitations).
- **Data Cleanup**: Automatically trim trailing rows that are entirely empty (None values).
- **Output**: Return a Pandas DataFrame representing the sheet's data.

### 3.2 Non-Functional Requirements

- **Performance**: Efficient for typical spreadsheet sizes (e.g., up to 10,000 rows); processing time should scale linearly with file size.
- **Security**: Use cryptography library for secure decryption; no storage of passwords or keys.
- **Error Handling**: Raise informative ValueErrors for issues like missing sheets, unsupported encryption, wrong passwords, or parsing failures.
- **Debugging**: Include print statements for key steps (e.g., detected algorithms, byte previews) to aid troubleshooting.
- **Compatibility**: Tested with ODS files from tools like LibreOffice; assumes ODF 1.2+ standards.


## 4. Technical Requirements

### 4.1 Implementation Details
- **Encryption Handling:** Parse `manifest.xml`; support PBKDF2 with SHA1/SHA256. Decrypt with AES-256-CBC or Blowfish CFB (with legacy adaptations such as conditional padding skip, decompression fallbacks, and flexible checksum verification). Decompress using zlib raw deflate.  
- **XML Parsing:** Use ElementTree with ODF namespaces to locate sheet.  
- **Data Processing:** Iterate rows/cells, expand repeats, build list-of-lists → DataFrame.  
- **Assumptions:** Supported algorithms: AES-256-CBC, Blowfish CFB. Others raise errors.  
- **Limitations:** No merged cells, formulas coputation, advanced styling interpretation.  
- **Header Handling**: The OpenDocument Spreadsheet specification does not define special semantics or markup for column headers (including multi-row headers). Consequently, this converter does not promote any row(s) to DataFrame column names. All header rows present in the .ods sheet are imported as regular DataFrame rows. Users may post-process in Pandas to set headers (e.g., using the first row as columns and dropping it), or construct a MultiIndex for multi-row headers. This behavior is by design and will only change if explicit header-handling options are added in future versions.  
- **Currency Symbol Inconsistency**: Currency values are extracted as strings preserving their display format, but currency symbol representation varies based on cell formatting:
    - Cells with explicit currency style (office:value-type="currency"): Display ISO currency codes (e.g., "USD 99.99")
    - Cells with currency formatting in general number style (office:value-type="float" with currency symbols): Display currency symbols (e.g., "$99.99")
    This inconsistency is acceptable for the current version. Future versions may implement currency symbol normalization, though this presents challenges for international currencies and right-to-left languages.
- **Debugging:** icecream.ic statements included.  

### 4.2 Testing Requirements
- **Unit Tests:** Unprotected files, protected (valid/invalid password), non-existent sheet, trimming, repeated rows/cols.  
- **Edge Cases:** Empty sheets, single-cell, large files, corrupted encryption.  
- **Integration Tests:** Compare DataFrame output vs LibreOffice exported CSV.  

## 5. Assumptions and Risks
- **Assumptions:** Users provide valid ODF files and correct passwords.  
- **Risks:** Variations in encryption across tools, performance for very large files.  
- **Mitigations:** Use fallback decompression; document supported encryption.  

## 6. Roadmap and Future Enhancements
- **Short-Term:** Configurable debug logging, improved error messages.  
- **Medium-Term:** Multi-sheet reading support, currency symbol normalization.  
- **Long-Term:** Full-featured ODS converter with merged cell support, advanced styling, optimize for scale.  


## 8. Approval and Sign-Off
**Status:** Draft.  
**Next Steps:** Review and iterate.  


**Last Updated:** 2025-09-03
