"""
Unit tests for read_ods_sheet_to_dataframe function and other functions in ods2df module
"""
import pytest
import pandas as pd
from pathlib import Path
import zipfile
from xml.etree import ElementTree as ET
import io
from ods2df.ods2df import read_ods_sheet_to_dataframe, decrypt_file, get_col_dec_places, extract_cell_text
from cryptography.hazmat.backends import default_backend


class TestReadOdsSheetToDataframe:
    """Test class for read_ods_sheet_to_dataframe function"""
    
    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"
    
    @pytest.fixture
    def expected_data(self):
        """Fixture to load expected data from CSV file"""
        csv_path = Path(__file__).parent / "resource" / "file_example_ODS_10.csv"
        return pd.read_csv(csv_path)

    @pytest.fixture
    def sample_data_types_expected(self):
        """Fixture to load expected data from sample_data_types CSV file"""
        csv_path = Path(__file__).parent / "resource" / "sample_data_types.csv"
        return pd.read_csv(csv_path)
    
    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"
    
    @pytest.fixture
    def password(self):
        """Fixture for the password used in protected files"""
        return "Overload_Idealize_Scale"
    
    def test_read_unprotected_ods_file(self, test_data_dir, expected_data, sheet_name):
        """Test reading an unprotected ODS file"""
        file_path = test_data_dir / "file_example_ODS_10.ods"

        # Read the ODS file
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"

        # The ODS function reads all rows as data (including headers), while CSV was exported with headers
        # So ODS DataFrame should have 1 more row than CSV (10 vs 9) but same number of columns
        expected_rows = expected_data.shape[0] + 1  # +1 because ODS includes header row as data
        expected_cols = expected_data.shape[1]
        assert df.shape == (expected_rows, expected_cols), f"Expected shape ({expected_rows}, {expected_cols}), got {df.shape}"

        # Verify the first row contains the headers
        expected_headers = ["0", "First Name", "Last Name", "Gender", "Country", "Age", "Date", "Id"]
        for j, expected_header in enumerate(expected_headers):
            actual_header = str(df.iloc[0, j])
            assert actual_header == expected_header, f"Header mismatch at col {j}: expected {expected_header}, got {actual_header}"

        # Verify the data content matches (starting from row 1 in ODS DataFrame vs row 0 in CSV)
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i + 1, j])  # +1 because ODS has header row at index 0
                assert expected_val == actual_val, f"Mismatch at data row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_read_protected_ods_file_aes256(self, test_data_dir, expected_data, sheet_name, password):
        """Test reading a password-protected ODS file with AES-256-CBC encryption"""
        file_path = test_data_dir / "file_example_ODS_10.protected.AES256.ods"

        # Read the protected ODS file with correct password
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)

        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"

        # The ODS function reads all rows as data (including headers), while CSV was exported with headers
        expected_rows = expected_data.shape[0] + 1  # +1 because ODS includes header row as data
        expected_cols = expected_data.shape[1]
        assert df.shape == (expected_rows, expected_cols), f"Expected shape ({expected_rows}, {expected_cols}), got {df.shape}"

        # Verify the data content matches (starting from row 1 in ODS DataFrame vs row 0 in CSV)
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i + 1, j])  # +1 because ODS has header row at index 0
                assert expected_val == actual_val, f"Mismatch at data row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_read_protected_ods_file_blowfish(self, test_data_dir, expected_data, sheet_name, password):
        """Test reading a password-protected ODS file with Blowfish CFB encryption"""
        file_path = test_data_dir / "file_example_ODS_10.protected.Blowfish.ods"

        # Read the protected ODS file with correct password
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)

        # Verify the DataFrame is not empty
        assert not df.empty, "DataFrame should not be empty"

        # The ODS function reads all rows as data (including headers), while CSV was exported with headers
        expected_rows = expected_data.shape[0] + 1  # +1 because ODS includes header row as data
        expected_cols = expected_data.shape[1]
        assert df.shape == (expected_rows, expected_cols), f"Expected shape ({expected_rows}, {expected_cols}), got {df.shape}"

        # Verify the data content matches (starting from row 1 in ODS DataFrame vs row 0 in CSV)
        for i in range(len(expected_data)):
            for j in range(len(expected_data.columns)):
                expected_val = str(expected_data.iloc[i, j])
                actual_val = str(df.iloc[i + 1, j])  # +1 because ODS has header row at index 0
                assert expected_val == actual_val, f"Mismatch at data row {i}, col {j}: expected {expected_val}, got {actual_val}"
    
    def test_protected_file_without_password(self, test_data_dir, sheet_name):
        """Test that reading a protected file without password raises ValueError"""
        file_path = test_data_dir / "file_example_ODS_10.protected.AES256.ods"
        
        with pytest.raises(ValueError, match="File is password-protected but no password provided"):
            read_ods_sheet_to_dataframe(str(file_path), sheet_name)
    
    def test_protected_file_with_wrong_password(self, test_data_dir, sheet_name):
        """Test that reading a protected file with wrong password raises an error"""
        file_path = test_data_dir / "file_example_ODS_10.protected.AES256.ods"
        wrong_password = "wrong_password"
        
        with pytest.raises((ValueError, Exception)):
            read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=wrong_password)
    
    def test_nonexistent_sheet_name(self, test_data_dir):
        """Test that requesting a non-existent sheet raises ValueError"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        nonexistent_sheet = "NonExistentSheet"
        
        with pytest.raises(ValueError, match=f"Sheet '{nonexistent_sheet}' not found"):
            read_ods_sheet_to_dataframe(str(file_path), nonexistent_sheet)
    
    def test_nonexistent_file(self, sheet_name):
        """Test that reading a non-existent file raises appropriate error"""
        nonexistent_file = "nonexistent_file.ods"
        
        with pytest.raises((FileNotFoundError, Exception)):
            read_ods_sheet_to_dataframe(nonexistent_file, sheet_name)
    
    def test_unprotected_file_with_password_warning(self, test_data_dir, expected_data, sheet_name, password, capsys):
        """Test that providing password for unprotected file shows warning but still works"""
        file_path = test_data_dir / "file_example_ODS_10.ods"

        # Read unprotected file with password (should show warning)
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=password)

        # Verify the DataFrame is correct despite the warning
        assert not df.empty, "DataFrame should not be empty"
        expected_rows = expected_data.shape[0] + 1  # +1 because ODS includes header row as data
        expected_cols = expected_data.shape[1]
        assert df.shape == (expected_rows, expected_cols), f"Expected shape ({expected_rows}, {expected_cols}), got {df.shape}"

        # Check that warning was printed
        captured = capsys.readouterr()
        assert "Warning: Password provided but file is not protected" in captured.out
    
    def test_dataframe_structure(self, test_data_dir, sheet_name):
        """Test that the returned object is a proper pandas DataFrame"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
        
        # Verify it's a DataFrame
        assert isinstance(df, pd.DataFrame), "Return value should be a pandas DataFrame"
        
        # Verify it has the expected number of rows and columns
        assert df.shape[0] == 10, f"Expected 10 rows, got {df.shape[0]}"
        assert df.shape[1] == 8, f"Expected 8 columns, got {df.shape[1]}"
    
    def test_data_types_and_content(self, test_data_dir, sheet_name):
        """Test that data types and content are correctly parsed"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        
        df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
        
        # Check first row (header row)
        expected_headers = ["0", "First Name", "Last Name", "Gender", "Country", "Age", "Date", "Id"]
        for i, expected_header in enumerate(expected_headers):
            assert str(df.iloc[0, i]) == expected_header, f"Header mismatch at column {i}"
        
        # Check a specific data row (row 1, which is the first data row)
        assert str(df.iloc[1, 0]) == "1", "First column should be '1'"
        assert str(df.iloc[1, 1]) == "Dulce", "Second column should be 'Dulce'"
        assert str(df.iloc[1, 2]) == "Abril", "Third column should be 'Abril'"
        assert str(df.iloc[1, 3]) == "Female", "Fourth column should be 'Female'"
    
    def test_all_files_produce_same_data(self, test_data_dir, expected_data, sheet_name, password):
        """Test that all ODS files (protected and unprotected) produce the same data"""
        files_to_test = [
            ("file_example_ODS_10.ods", None),
            ("file_example_ODS_10.protected.AES256.ods", password),
            ("file_example_ODS_10.protected.Blowfish.ods", password)
        ]
        
        dataframes = []
        
        for filename, file_password in files_to_test:
            file_path = test_data_dir / filename
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name, password=file_password)
            dataframes.append(df)
        
        # Compare all dataframes to ensure they're identical
        base_df = dataframes[0]
        for i, df in enumerate(dataframes[1:], 1):
            assert df.shape == base_df.shape, f"DataFrame {i} shape mismatch"
            
            # Compare content
            for row in range(len(df)):
                for col in range(len(df.columns)):
                    base_val = str(base_df.iloc[row, col])
                    current_val = str(df.iloc[row, col])
                    assert base_val == current_val, f"Data mismatch in file {i} at row {row}, col {col}"


class TestDataTypesHandling:
    """Test class for data type handling in read_ods_sheet_to_dataframe function"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"

    def test_various_data_types_parsing(self, test_data_dir, sheet_name):
        """Test that various data types are correctly parsed from sample_data_types.ods"""
        file_path = test_data_dir / "sample_data_types.ods"

        # Disable icecream debug output for cleaner test output
        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Verify the DataFrame structure (updated for new data types)
            assert df.shape == (10, 2), f"Expected shape (10, 2), got {df.shape}"

            # Verify headers
            assert str(df.iloc[0, 0]) == "Data Type", "First header should be 'Data Type'"
            assert str(df.iloc[0, 1]) == "Value", "Second header should be 'Value'"

            # Test Float data type
            float_value = df.iloc[1, 1]
            assert isinstance(float_value, float), f"Float value should be float type, got {type(float_value)}"
            assert float_value == 123.45, f"Float value should be 123.45, got {float_value}"

            # Test Percentage data type (should be string with % symbol)
            percentage_value = df.iloc[2, 1]
            assert isinstance(percentage_value, str), f"Percentage should be string type, got {type(percentage_value)}"
            assert percentage_value == "75%", f"Percentage should be '75%', got {percentage_value}"

            # Test Currency in currency style (should be string with ISO currency code)
            currency_style_value = df.iloc[3, 1]
            assert isinstance(currency_style_value, str), f"Currency style should be string type, got {type(currency_style_value)}"
            assert currency_style_value == "USD 99.99", f"Currency style should be 'USD 99.99', got {currency_style_value}"

            # Test Currency in general style (should be string with currency symbol)
            currency_general_value = df.iloc[4, 1]
            assert isinstance(currency_general_value, str), f"Currency general should be string type, got {type(currency_general_value)}"
            assert currency_general_value == "$99.99", f"Currency general should be '$99.99', got {currency_general_value}"

            # Test Date data type (should be string in ISO format)
            date_value = df.iloc[5, 1]
            assert isinstance(date_value, str), f"Date should be string type, got {type(date_value)}"
            assert date_value == "2025-08-21", f"Date should be '2025-08-21', got {date_value}"

            # Test Time data type (should be string in HH:MM:SS format)
            time_value = df.iloc[6, 1]
            assert isinstance(time_value, str), f"Time should be string type, got {type(time_value)}"
            assert time_value == "12:30:00", f"Time should be '12:30:00', got {time_value}"

            # Test Boolean data type
            boolean_value = df.iloc[7, 1]
            assert isinstance(boolean_value, bool), f"Boolean should be bool type, got {type(boolean_value)}"
            assert boolean_value is True, f"Boolean should be True, got {boolean_value}"

            # Test Formula data type (should be string with formula)
            formula_value = df.iloc[8, 1]
            assert isinstance(formula_value, str), f"Formula should be string type, got {type(formula_value)}"
            assert formula_value.startswith("="), f"Formula should start with '=', got {formula_value}"
            assert "SUM" in formula_value, f"Formula should contain 'SUM', got {formula_value}"

            # Test Multi Line String data type (should be string with newlines in correct order)
            multiline_value = df.iloc[9, 1]
            assert isinstance(multiline_value, str), f"Multi-line string should be string type, got {type(multiline_value)}"
            assert "\n" in multiline_value, f"Multi-line string should contain newlines, got {multiline_value}"

            # Verify exact content and line order
            expected_multiline = "Line 1.\nLine 2.\nLine 3."
            assert multiline_value == expected_multiline, f"Multi-line string should be '{expected_multiline}', got '{multiline_value}'"

            # Verify line order by checking positions
            line1_pos = multiline_value.find("Line 1.")
            line2_pos = multiline_value.find("Line 2.")
            line3_pos = multiline_value.find("Line 3.")
            assert line1_pos < line2_pos < line3_pos, f"Lines should be in order 1,2,3 at positions {line1_pos},{line2_pos},{line3_pos}"

        finally:
            # Re-enable icecream for other tests
            ic.enable()

    def test_data_type_consistency(self, test_data_dir, sheet_name):
        """Test that data types are consistently parsed across multiple reads"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            # Read the file multiple times
            df1 = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
            df2 = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Compare data types
            for i in range(len(df1)):
                for j in range(len(df1.columns)):
                    val1 = df1.iloc[i, j]
                    val2 = df2.iloc[i, j]
                    assert type(val1) == type(val2), f"Type mismatch at ({i}, {j}): {type(val1)} vs {type(val2)}"
                    assert val1 == val2 or (pd.isna(val1) and pd.isna(val2)), f"Value mismatch at ({i}, {j}): {val1} vs {val2}"

        finally:
            ic.enable()

    def test_currency_styles_handling(self, test_data_dir, sheet_name):
        """Test that both currency styles are correctly handled"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find currency style row (should be row 3)
            currency_style_row = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Currency in currency style":
                    currency_style_row = i
                    break

            assert currency_style_row is not None, "Should find 'Currency in currency style' row"
            currency_style_value = df.iloc[currency_style_row, 1]
            assert isinstance(currency_style_value, str), f"Currency style should be string, got {type(currency_style_value)}"
            assert currency_style_value == "USD 99.99", f"Currency style should be 'USD 99.99', got {currency_style_value}"

            # Find currency general row (should be row 4)
            currency_general_row = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Currency in general style":
                    currency_general_row = i
                    break

            assert currency_general_row is not None, "Should find 'Currency in general style' row"
            currency_general_value = df.iloc[currency_general_row, 1]
            assert isinstance(currency_general_value, str), f"Currency general should be string, got {type(currency_general_value)}"
            assert currency_general_value == "$99.99", f"Currency general should be '$99.99', got {currency_general_value}"

        finally:
            ic.enable()

    def test_multiline_string_handling(self, test_data_dir, sheet_name):
        """Test that multi-line strings are correctly parsed with newlines in correct order"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find multi-line string row (should be row 9)
            multiline_row = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Multi Line String":
                    multiline_row = i
                    break

            assert multiline_row is not None, "Should find 'Multi Line String' row"
            multiline_value = df.iloc[multiline_row, 1]

            # Verify it's a string
            assert isinstance(multiline_value, str), f"Multi-line string should be string type, got {type(multiline_value)}"

            # Verify it contains newlines
            assert "\n" in multiline_value, f"Should contain newlines, got {multiline_value}"

            # Verify the exact format and line order
            expected_content = "Line 1.\nLine 2.\nLine 3."
            assert multiline_value == expected_content, f"Expected '{expected_content}', got '{multiline_value}'"

            # Additional line order verification by splitting and checking each line
            lines = multiline_value.split('\n')
            assert len(lines) == 3, f"Should have exactly 3 lines, got {len(lines)}: {lines}"
            assert lines[0] == "Line 1.", f"First line should be 'Line 1.', got '{lines[0]}'"
            assert lines[1] == "Line 2.", f"Second line should be 'Line 2.', got '{lines[1]}'"
            assert lines[2] == "Line 3.", f"Third line should be 'Line 3.', got '{lines[2]}'"

            # Verify line order by checking positions
            line1_pos = multiline_value.find("Line 1.")
            line2_pos = multiline_value.find("Line 2.")
            line3_pos = multiline_value.find("Line 3.")

            assert line1_pos < line2_pos, f"Line 1 should come before Line 2: positions {line1_pos}, {line2_pos}"
            assert line2_pos < line3_pos, f"Line 2 should come before Line 3: positions {line2_pos}, {line3_pos}"
            assert line1_pos < line3_pos, f"Line 1 should come before Line 3: positions {line1_pos}, {line3_pos}"

        finally:
            ic.enable()

    def test_formula_preservation_with_brackets(self, test_data_dir, sheet_name):
        """Test that formulas are preserved with OpenDocument format (square brackets)"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find formula row (should be row 8)
            formula_row = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Formula":
                    formula_row = i
                    break

            assert formula_row is not None, "Should find 'Formula' row"
            formula_value = df.iloc[formula_row, 1]

            # Verify it's a string starting with =
            assert isinstance(formula_value, str), f"Formula should be string type, got {type(formula_value)}"
            assert formula_value.startswith("="), f"Formula should start with '=', got {formula_value}"

            # Verify it contains SUM and square brackets (OpenDocument format)
            assert "SUM" in formula_value, f"Formula should contain 'SUM', got {formula_value}"
            assert "[" in formula_value and "]" in formula_value, f"Formula should contain square brackets, got {formula_value}"

            # Verify the exact format (as shown in the example output)
            expected_formula = "=SUM([.B2:.B3])"
            assert formula_value == expected_formula, f"Expected '{expected_formula}', got '{formula_value}'"

        finally:
            ic.enable()


class TestDecryptFileFunction:
    """Test class for decrypt_file function"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def password(self):
        """Fixture for the password used in protected files"""
        return "Overload_Idealize_Scale"

    def test_decrypt_unprotected_file(self, test_data_dir):
        """Test decrypt_file with an unprotected file"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            # Extract manifest.xml
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Test decrypting content.xml from unprotected file
            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)

            # Verify it's valid XML
            assert decrypted_xml is not None, "Decrypted XML should not be None"
            assert isinstance(decrypted_xml, bytes), "Decrypted XML should be bytes"

            # Parse to verify it's valid XML
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()
            assert content_root is not None, "Should be able to parse decrypted XML"

    def test_decrypt_protected_file_aes256(self, test_data_dir, password):
        """Test decrypt_file with AES-256 protected file"""
        file_path = test_data_dir / "file_example_ODS_10.protected.AES256.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Test decrypting with correct password
            decrypted_xml = decrypt_file(zf, 'content.xml', password, manifest_root, manifest_ns, backend)

            assert decrypted_xml is not None, "Decrypted XML should not be None"
            assert isinstance(decrypted_xml, bytes), "Decrypted XML should be bytes"

            # Parse to verify it's valid XML
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()
            assert content_root is not None, "Should be able to parse decrypted XML"

    def test_decrypt_protected_file_without_password(self, test_data_dir):
        """Test decrypt_file raises error when password is required but not provided"""
        file_path = test_data_dir / "file_example_ODS_10.protected.AES256.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Test that it raises ValueError when no password provided
            with pytest.raises(ValueError, match="File is password-protected but no password provided"):
                decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)

    def test_decrypt_nonexistent_file(self, test_data_dir):
        """Test decrypt_file raises error for non-existent file in manifest"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Test that it raises ValueError for non-existent file
            with pytest.raises(ValueError, match="nonexistent.xml not found in manifest"):
                decrypt_file(zf, 'nonexistent.xml', None, manifest_root, manifest_ns, backend)


class TestGetColDecPlacesFunction:
    """Test class for get_col_dec_places function"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"

    def test_get_col_dec_places_basic(self, test_data_dir, sheet_name):
        """Test get_col_dec_places with basic ODS file"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Decrypt and read content.xml
            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()

            # Test get_col_dec_places
            col_dec_places = get_col_dec_places(content_root, sheet_name)

            # Verify it returns a list
            assert isinstance(col_dec_places, list), "Should return a list"
            assert len(col_dec_places) > 0, "Should return non-empty list"

            # Each element should be either a string or number
            for dec_place in col_dec_places:
                assert isinstance(dec_place, (str, int)), f"Each element should be string or int, got {type(dec_place)}"

    def test_get_col_dec_places_nonexistent_sheet(self, test_data_dir):
        """Test get_col_dec_places raises error for non-existent sheet"""
        file_path = test_data_dir / "file_example_ODS_10.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()

            # Test with non-existent sheet
            with pytest.raises(ValueError, match="Sheet 'NonExistentSheet' not found"):
                get_col_dec_places(content_root, "NonExistentSheet")

    def test_get_col_dec_places_sample_data_types(self, test_data_dir, sheet_name):
        """Test get_col_dec_places with sample_data_types.ods"""
        file_path = test_data_dir / "sample_data_types.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()

            # Test get_col_dec_places
            col_dec_places = get_col_dec_places(content_root, sheet_name)

            # Should return a list (may be empty if no column-level decimal settings)
            assert isinstance(col_dec_places, list), "Should return a list"
            # Note: sample_data_types.ods may not have column-level decimal place settings,
            # so the list could be empty. This is expected behavior for files without
            # explicit column formatting.


class TestMultiLineStringRegression:
    """Test class for multi-line string regression fix"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"

    def test_multiline_string_regression_fix(self, test_data_dir, sheet_name):
        """Test that multi-line strings are correctly extracted from new_line_regress.ods

        This test verifies the fix for the extract_cell_text function that was
        previously only extracting partial content from multi-line cells.
        """
        file_path = test_data_dir / "new_line_regress.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Verify the DataFrame structure
            assert df.shape == (1, 3), f"Expected shape (1, 3), got {df.shape}"

            # Verify the first two cells are correct
            assert str(df.iloc[0, 0]) == "ETH", f"First cell should be 'ETH', got {df.iloc[0, 0]}"
            assert df.iloc[0, 1] == 456, f"Second cell should be 456, got {df.iloc[0, 1]}"

            # Test the multi-line string cell (the main regression test)
            multiline_cell = df.iloc[0, 2]
            assert isinstance(multiline_cell, str), f"Multi-line cell should be string, got {type(multiline_cell)}"

            # Verify the complete content is extracted (not just partial like '\nFor ')
            expected_content = "Registered at CoinTracking on Apr 12th '25.\nFor HODL"
            assert multiline_cell == expected_content, f"Expected '{expected_content}', got '{multiline_cell}'"

            # Verify it contains newlines
            assert "\n" in multiline_cell, f"Multi-line string should contain newlines, got {multiline_cell}"

            # Verify line order and content
            lines = multiline_cell.split('\n')
            assert len(lines) == 2, f"Should have exactly 2 lines, got {len(lines)}: {lines}"
            assert lines[0] == "Registered at CoinTracking on Apr 12th '25.", f"First line should be 'Registered at CoinTracking on Apr 12th '25.', got '{lines[0]}'"
            assert lines[1] == "For HODL", f"Second line should be 'For HODL', got '{lines[1]}'"

            # Verify line order by checking positions
            line1_pos = multiline_cell.find("Registered at CoinTracking")
            line2_pos = multiline_cell.find("For HODL")
            assert line1_pos < line2_pos, f"First line should come before second line: positions {line1_pos}, {line2_pos}"

        finally:
            ic.enable()

    def test_multiline_string_no_truncation(self, test_data_dir, sheet_name):
        """Test that multi-line strings are not truncated or corrupted

        This specifically tests against the bug where only '\nFor ' was extracted
        instead of the full multi-line content.
        """
        file_path = test_data_dir / "new_line_regress.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
            multiline_cell = df.iloc[0, 2]

            # Ensure we don't get the truncated version that was the bug
            assert multiline_cell != "\nFor ", "Should not get truncated content '\nFor '"
            assert multiline_cell != "\\nFor ", "Should not get escaped truncated content '\\nFor '"

            # Ensure we get the full content
            assert "Registered at CoinTracking" in multiline_cell, "Should contain the first line content"
            assert "For HODL" in multiline_cell, "Should contain the second line content"
            assert "Apr 12th '25" in multiline_cell, "Should contain the date part"

            # Ensure the content starts with the first line, not a newline
            assert not multiline_cell.startswith('\n'), "Content should not start with newline"
            assert multiline_cell.startswith('Registered'), "Content should start with 'Registered'"

        finally:
            ic.enable()

    def test_multiline_string_preserve_formatting(self, test_data_dir, sheet_name):
        """Test that multi-line strings preserve original formatting and line breaks"""
        file_path = test_data_dir / "new_line_regress.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)
            multiline_cell = df.iloc[0, 2]

            # Test that the newline character is properly preserved
            newline_count = multiline_cell.count('\n')
            assert newline_count == 1, f"Should have exactly 1 newline character, got {newline_count}"

            # Test that there are no extra whitespace issues
            lines = multiline_cell.split('\n')
            for i, line in enumerate(lines):
                # Lines should not have leading/trailing whitespace after split
                # (the extract_cell_text function should handle this properly)
                stripped_line = line.strip()
                if i == 0:
                    assert "Registered at CoinTracking" in stripped_line, f"First line should contain registration text, got '{stripped_line}'"
                elif i == 1:
                    assert stripped_line == "For HODL", f"Second line should be 'For HODL', got '{stripped_line}'"

        finally:
            ic.enable()


class TestExtractCellTextFunction:
    """Test class for extract_cell_text function specifically"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def namespaces(self):
        """Fixture for XML namespaces"""
        return {
            'table': 'urn:oasis:names:tc:opendocument:xmlns:table:1.0',
            'office': 'urn:oasis:names:tc:opendocument:xmlns:office:1.0',
            'style': 'urn:oasis:names:tc:opendocument:xmlns:style:1.0',
            'number': 'urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0',
            'fo': 'urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0',
            'text': 'urn:oasis:names:tc:opendocument:xmlns:text:1.0',
            'calc': 'urn:org:documentfoundation:names:experimental:calc:xmlns:calc:1.0'
        }

    def test_extract_cell_text_with_multiline_content(self, test_data_dir, namespaces):
        """Test extract_cell_text function directly with multi-line content from new_line_regress.ods"""
        file_path = test_data_dir / "new_line_regress.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            # Extract manifest.xml
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}

            # Decrypt and read content.xml
            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()

            # Find the sheet
            spreadsheet = content_root.find('.//office:body/office:spreadsheet', namespaces)
            assert spreadsheet is not None, "Should find spreadsheet"

            sheet = None
            for s in spreadsheet.findall('table:table', namespaces):
                if s.get(f"{{{namespaces['table']}}}name") == "Sheet1":
                    sheet = s
                    break
            assert sheet is not None, "Should find Sheet1"

            # Find the first row
            row_elem = sheet.find('table:table-row', namespaces)
            assert row_elem is not None, "Should find first row"

            # Find all cells in the row
            cell_elems = list(row_elem.findall('table:table-cell', namespaces))
            assert len(cell_elems) >= 3, f"Should have at least 3 cells, got {len(cell_elems)}"

            # Test the third cell (index 2) which contains the multi-line string
            multiline_cell_elem = cell_elems[2]

            # Test extract_cell_text function directly
            extracted_text = extract_cell_text(multiline_cell_elem, namespaces)

            # Verify the extracted text
            assert isinstance(extracted_text, str), f"Should return string, got {type(extracted_text)}"
            assert extracted_text != "", "Should not return empty string"

            # Verify complete content extraction
            expected_content = "Registered at CoinTracking on Apr 12th '25.\nFor HODL"
            assert extracted_text == expected_content, f"Expected '{expected_content}', got '{extracted_text}'"

            # Verify it's not the truncated version that was the bug
            assert extracted_text != "\nFor ", "Should not return truncated content"
            assert extracted_text != "\\nFor ", "Should not return escaped truncated content"

    def test_extract_cell_text_handles_multiple_paragraphs(self, test_data_dir, namespaces):
        """Test that extract_cell_text properly handles multiple text:p elements"""
        file_path = test_data_dir / "new_line_regress.ods"
        backend = default_backend()

        with zipfile.ZipFile(file_path, 'r') as zf:
            with zf.open('META-INF/manifest.xml') as manifest_file:
                manifest_tree = ET.parse(manifest_file)
                manifest_root = manifest_tree.getroot()

            manifest_ns = {'manifest': 'urn:oasis:names:tc:opendocument:xmlns:manifest:1.0'}
            decrypted_xml = decrypt_file(zf, 'content.xml', None, manifest_root, manifest_ns, backend)
            content_tree = ET.parse(io.BytesIO(decrypted_xml))
            content_root = content_tree.getroot()

            # Find the multi-line cell
            spreadsheet = content_root.find('.//office:body/office:spreadsheet', namespaces)
            sheet = spreadsheet.find('table:table[@table:name="Sheet1"]', namespaces)
            row_elem = sheet.find('table:table-row', namespaces)
            cell_elems = list(row_elem.findall('table:table-cell', namespaces))
            multiline_cell_elem = cell_elems[2]

            # Check that the cell actually contains multiple text:p elements
            p_elements = multiline_cell_elem.findall('text:p', namespaces)
            assert len(p_elements) >= 1, f"Should have at least 1 text:p element, got {len(p_elements)}"

            # Test that extract_cell_text processes all paragraphs
            extracted_text = extract_cell_text(multiline_cell_elem, namespaces)

            # Verify that all paragraph content is included
            lines = extracted_text.split('\n')
            assert len(lines) == 2, f"Should have 2 lines, got {len(lines)}: {lines}"

            # Verify line order is preserved
            assert lines[0].strip() == "Registered at CoinTracking on Apr 12th '25.", f"First line incorrect: '{lines[0]}'"
            assert lines[1].strip() == "For HODL", f"Second line incorrect: '{lines[1]}'"

    def test_extract_cell_text_empty_cell(self, namespaces):
        """Test extract_cell_text with empty cell"""
        # Create a mock empty cell element
        cell_elem = ET.Element('table:table-cell')

        result = extract_cell_text(cell_elem, namespaces)
        assert result == "", "Empty cell should return empty string"

    def test_extract_cell_text_single_paragraph(self, namespaces):
        """Test extract_cell_text with single paragraph"""
        # Create a mock cell with single paragraph
        cell_elem = ET.Element('table:table-cell')
        p_elem = ET.SubElement(cell_elem, f"{{{namespaces['text']}}}p")
        p_elem.text = "Single line text"

        result = extract_cell_text(cell_elem, namespaces)
        assert result == "Single line text", f"Expected 'Single line text', got '{result}'"


class TestEdgeCasesAndIntegration:
    """Test class for edge cases and integration scenarios"""

    @pytest.fixture
    def test_data_dir(self):
        """Fixture to get the test data directory path"""
        return Path(__file__).parent / "resource"

    @pytest.fixture
    def sheet_name(self):
        """Fixture for the sheet name"""
        return "Sheet1"

    def test_empty_cells_handling(self, test_data_dir, sheet_name):
        """Test that empty cells are handled correctly"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Check that the DataFrame handles empty cells properly
            # (The sample file should not have empty cells, but we test the structure)
            assert df is not None, "DataFrame should not be None"
            assert not df.empty, "DataFrame should not be empty"

            # Verify no unexpected None values in data rows
            for i in range(1, len(df)):  # Skip header row
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    # Value should not be None for our test data
                    assert value is not None, f"Unexpected None value at ({i}, {j})"

        finally:
            ic.enable()

    def test_formula_preservation(self, test_data_dir, sheet_name):
        """Test that formulas are preserved as strings"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find the formula row (should be the last row with "Formula" in first column)
            formula_row_idx = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Formula":
                    formula_row_idx = i
                    break

            assert formula_row_idx is not None, "Should find Formula row"

            # Check that the formula is preserved as a string
            formula_value = df.iloc[formula_row_idx, 1]
            assert isinstance(formula_value, str), f"Formula should be string, got {type(formula_value)}"
            assert formula_value.startswith("="), f"Formula should start with '=', got {formula_value}"

        finally:
            ic.enable()

    def test_numeric_precision_handling(self, test_data_dir, sheet_name):
        """Test that numeric values maintain proper precision"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find the float value (should be 123.45)
            float_row_idx = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Float":
                    float_row_idx = i
                    break

            assert float_row_idx is not None, "Should find Float row"

            float_value = df.iloc[float_row_idx, 1]
            assert isinstance(float_value, float), f"Float value should be float type, got {type(float_value)}"
            assert abs(float_value - 123.45) < 0.0001, f"Float value should be 123.45, got {float_value}"

        finally:
            ic.enable()

    def test_boolean_type_handling(self, test_data_dir, sheet_name):
        """Test that boolean values are correctly parsed"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Find the boolean value
            boolean_row_idx = None
            for i in range(len(df)):
                if str(df.iloc[i, 0]) == "Boolean":
                    boolean_row_idx = i
                    break

            assert boolean_row_idx is not None, "Should find Boolean row"

            boolean_value = df.iloc[boolean_row_idx, 1]
            assert isinstance(boolean_value, bool), f"Boolean value should be bool type, got {type(boolean_value)}"
            assert boolean_value is True, f"Boolean value should be True, got {boolean_value}"

        finally:
            ic.enable()

    def test_string_data_preservation(self, test_data_dir, sheet_name):
        """Test that string data is preserved correctly"""
        file_path = test_data_dir / "sample_data_types.ods"

        from icecream import ic
        ic.disable()

        try:
            df = read_ods_sheet_to_dataframe(str(file_path), sheet_name)

            # Check headers are strings
            assert isinstance(df.iloc[0, 0], str), "Header should be string"
            assert isinstance(df.iloc[0, 1], str), "Header should be string"

            # Check data type labels are strings
            for i in range(1, len(df)):
                data_type_label = df.iloc[i, 0]
                assert isinstance(data_type_label, str), f"Data type label should be string, got {type(data_type_label)}"

        finally:
            ic.enable()

    def test_compare_with_csv_reference(self, test_data_dir, sheet_name):
        """Test that ODS parsing matches CSV reference for sample_data_types"""
        ods_file_path = test_data_dir / "sample_data_types.ods"
        csv_file_path = test_data_dir / "sample_data_types.csv"

        from icecream import ic
        ic.disable()

        try:
            # Read ODS file
            ods_df = read_ods_sheet_to_dataframe(str(ods_file_path), sheet_name)

            # Read CSV file
            csv_df = pd.read_csv(csv_file_path)

            # Compare structure (ODS includes header as data row, CSV has headers)
            assert ods_df.shape[0] == csv_df.shape[0] + 1, "ODS should have one more row than CSV (header row)"
            assert ods_df.shape[1] == csv_df.shape[1], "ODS and CSV should have same number of columns"

            # Compare headers (ODS first row vs CSV column names)
            assert str(ods_df.iloc[0, 0]) == csv_df.columns[0], "First header should match"
            assert str(ods_df.iloc[0, 1]) == csv_df.columns[1], "Second header should match"

            # Compare data content (accounting for type differences)
            for i in range(len(csv_df)):
                csv_row = csv_df.iloc[i]
                ods_row = ods_df.iloc[i + 1]  # +1 because ODS has header row

                # Compare first column (data type labels)
                assert str(ods_row.iloc[0]) == str(csv_row.iloc[0]), f"Data type mismatch at row {i}"

                # For the value column, we need to handle type differences
                csv_value = str(csv_row.iloc[1])
                ods_value = ods_row.iloc[1]

                # Special handling for different data types
                if csv_value == "123.45":
                    assert isinstance(ods_value, float) and ods_value == 123.45
                elif csv_value == "TRUE":
                    assert isinstance(ods_value, bool) and ods_value is True
                elif csv_value.startswith("="):
                    # Handle formula differences: ODS preserves OpenDocument format with square brackets
                    # while CSV export converts to Excel-style format
                    assert isinstance(ods_value, str) and ods_value.startswith("=")
                    # For formulas, we expect the ODS to have square brackets that CSV doesn't have
                    if "SUM" in csv_value and "SUM" in str(ods_value):
                        # Both should contain SUM, but ODS may have [.B2:.B3] while CSV has B2:B3
                        assert "SUM" in str(ods_value), f"Formula should contain 'SUM', got {ods_value}"
                    else:
                        assert str(ods_value) == csv_value
                elif csv_value == "$99.99":
                    # Currency handling: Both currency styles in ODS should map to $99.99 in CSV
                    # because LibreOffice exports both currency formats as $99.99
                    assert isinstance(ods_value, str), f"Currency should be string, got {type(ods_value)}"
                    assert "99.99" in str(ods_value), f"Currency should contain '99.99', got {ods_value}"
                elif "Line 1." in csv_value and "Line 2." in csv_value:
                    # Multi-line string handling
                    assert isinstance(ods_value, str), f"Multi-line string should be string, got {type(ods_value)}"
                    assert "Line 1." in str(ods_value), f"Should contain 'Line 1.', got {ods_value}"
                    assert "Line 2." in str(ods_value), f"Should contain 'Line 2.', got {ods_value}"
                    assert "Line 3." in str(ods_value), f"Should contain 'Line 3.', got {ods_value}"
                else:
                    # For other types, compare as strings
                    assert str(ods_value) == csv_value or csv_value in str(ods_value)

        finally:
            ic.enable()
